# Stop-N8N-NgRok.ps1
# Automated shutdown script for n8n Docker + nGrok tunnel

param(
    [switch]$KeepDocker,
    [switch]$Verbose
)

# Configuration
$N8N_DOCKER_PATH = "C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n-docker"

Write-Host "🛑 Stopping n8n + nGrok Services" -ForegroundColor Red
Write-Host "=" * 40

# Function to stop nGrok processes
function Stop-NgrokProcesses {
    Write-Host "🌐 Stopping nGrok processes..." -ForegroundColor Yellow
    
    try {
        # Find and stop nGrok processes
        $ngrokProcesses = Get-Process -Name "ngrok" -ErrorAction SilentlyContinue
        
        if ($ngrokProcesses) {
            foreach ($process in $ngrokProcesses) {
                Write-Host "🔄 Stopping nGrok process (PID: $($process.Id))" -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force
            }
            Write-Host "✅ nGrok processes stopped" -ForegroundColor Green
        }
        else {
            Write-Host "ℹ️ No nGrok processes found running" -ForegroundColor Cyan
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Error stopping nGrok: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to stop Docker containers
function Stop-DockerContainers {
    Write-Host "📦 Stopping Docker containers..." -ForegroundColor Yellow
    
    try {
        Set-Location $N8N_DOCKER_PATH
        
        # Stop containers
        docker-compose down
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker containers stopped successfully" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Failed to stop Docker containers" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error stopping Docker containers: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to show final status
function Show-Status {
    Write-Host ""
    Write-Host "📊 Final Status:" -ForegroundColor Cyan
    Write-Host "-" * 20
    
    # Check nGrok
    $ngrokRunning = Get-Process -Name "ngrok" -ErrorAction SilentlyContinue
    if ($ngrokRunning) {
        Write-Host "🌐 nGrok: Still running" -ForegroundColor Yellow
    }
    else {
        Write-Host "🌐 nGrok: Stopped" -ForegroundColor Green
    }
    
    # Check Docker containers
    try {
        $n8nRunning = docker ps --filter "name=n8n" --format "{{.Names}}" 2>$null
        if ($n8nRunning -contains "n8n-dev") {
            Write-Host "📦 n8n Docker: Still running" -ForegroundColor Yellow
        }
        else {
            Write-Host "📦 n8n Docker: Stopped" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "📦 n8n Docker: Status unknown" -ForegroundColor Gray
    }
}

# Main execution
try {
    # Stop nGrok first
    Stop-NgrokProcesses | Out-Null
    
    # Stop Docker containers (unless keeping them)
    if (-not $KeepDocker) {
        Stop-DockerContainers | Out-Null
    }
    else {
        Write-Host "ℹ️ Keeping Docker containers running (--KeepDocker specified)" -ForegroundColor Cyan
    }
    
    # Show final status
    Show-Status
    
    Write-Host ""
    Write-Host "🎉 Shutdown Complete!" -ForegroundColor Green
    
    if ($KeepDocker) {
        Write-Host "💡 n8n is still accessible at: http://localhost:5678" -ForegroundColor Cyan
    }
}
catch {
    Write-Host "❌ Shutdown script failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
